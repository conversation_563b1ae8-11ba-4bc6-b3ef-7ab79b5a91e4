{"name": "vite-project", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite --mode development", "start": "vite --mode production", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@fortawesome/fontawesome-svg-core": "^6.7.2", "@fortawesome/free-solid-svg-icons": "^6.7.2", "@fortawesome/react-fontawesome": "^0.2.2", "@highcharts/react": "^4.0.0-beta.4", "@mapbox/rehype-prism": "^0.9.0", "@react-three/drei": "^10.3.0", "@react-three/fiber": "^9.1.2", "@reduxjs/toolkit": "^2.8.2", "@tailwindcss/vite": "^4.1.8", "axios": "^1.10.0", "chart.js": "^4.4.1", "chartjs-plugin-datalabels": "^2.2.0", "clsx": "^2.1.1", "date-fns": "^4.1.0", "firebase": "^11.10.0", "framer-motion": "^12.23.0", "highcharts": "^12.3.0", "highcharts-react-official": "^3.2.2", "katex": "^0.16.22", "livekit-client": "^2.13.5", "lottie-react": "^2.4.1", "lucide-react": "^0.513.0", "motion": "^12.16.0", "prop-types": "^15.8.1", "react": "^19.1.0", "react-chartjs-2": "^5.3.0", "react-circular-progressbar": "^2.2.0", "react-dom": "^19.1.0", "react-icons": "^5.5.0", "react-katex": "^3.1.0", "react-latex": "^2.0.0", "react-markdown": "^10.1.0", "react-parallax-tilt": "^1.7.300", "react-pdf": "^9.2.1", "react-redux": "^9.2.0", "react-router": "^7.6.2", "react-router-dom": "^7.6.3", "react-select": "^5.10.1", "react-toastify": "^11.0.5", "react-tooltip": "^5.28.1", "rehype-katex": "^7.0.1", "rehype-mathjax": "^7.1.0", "rehype-raw": "^7.0.0", "remark-math": "^6.0.0", "socket.io-client": "^4.8.1", "tailwind-merge": "^3.3.1", "tailwindcss": "^4.1.8", "three": "^0.177.0", "three-stdlib": "^2.36.0", "zustand": "^5.0.5"}, "devDependencies": {"@eslint/js": "^9.25.0", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "@vitejs/plugin-react-swc": "^3.9.0", "eslint": "^9.25.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "prettier": "3.5.3", "vite": "^6.3.5"}}