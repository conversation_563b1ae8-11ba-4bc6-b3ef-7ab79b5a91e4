import React from 'react';
import { Navigate, useRoutes } from 'react-router';
import LandPage from './pages/LandPages/LandPage';
import Login from './pages/auth/Login';
import DirectorDashboard from './pages/screens/directorPanel/directorPanelDashboard/DirectorPanelDashboard';
import CentreCounselorDashboard from './pages/screens/centreCounselorPanel/centreCounselorDashboard/CentreCounselorDashboard';
import StudentsDashboard from './pages/screens/studentPanel/studentsDashboard/StudentsDashboard';
import NeetPrepHome from './pages/LandPages/NeetPrepHome';
import PageNotFound from './pages/auth/PageNotFound';
import Home from './pages/Home';
import Inbox from './pages/screens/directorPanel/inbox/Inbox';
import AddCenter from './pages/screens/directorPanel/addCenter/AddCenter';
import AddKotaTeachers from './pages/screens/directorPanel/addKotaTeachers/AddKotaTeachers';
import ListCenters from './pages/screens/directorPanel/listCenters/ListCenters';
import ListKotaTeachers from './pages/screens/directorPanel/listKotaTeachers/ListKotaTeachers';
import RecordedVideos from './pages/screens/studentPanel/recordedVideos/RecordedVideos';
import LiveStreaming from './pages/screens/studentPanel/liveStreaming/LiveStreaming';
import EBookCentre from './pages/screens/studentPanel/eBookCentre/EBookCentre';
import CreateYourOwnTest from './pages/screens/studentPanel/createYourOwnTest/CreateYourOwnTest';
import MockTestSimulation from './pages/screens/studentPanel/mockTestSimulation/MockTestSimulation';
import BoosterModule from './pages/screens/studentPanel/boosterModule/BoosterModule';
import StudentCommunity from './pages/screens/studentPanel/studentCommunity/StudentCommunity';
import AddStudents from './pages/screens/centreCounselorPanel/addStudents/AddStudents';
import AddFaculty from './pages/screens/centreCounselorPanel/addFaculty/AddFaculty';
import ListStudents from './pages/screens/centreCounselorPanel/listStudents/ListStudents';
import ListFaculty from './pages/screens/centreCounselorPanel/listFaculty/ListFaculty';
import KotaTeachers from './pages/screens/centreCounselorPanel/kotaTeachers/KotaTeachers';
import ProtectedRoute from './pages/auth/ProtectedRoute';
import Dashboard from './pages/screens/Dashboard/Dashboard';
import ProcessSelector from './pages/screens/directorPanel/processSelector/ProcessSelector';
import TeacherLiveStreaming from './pages/screens/teacherPanel/teacherLiveStreaming/TeacherLiveStreaming';
import MappedCenters from './pages/screens/teacherPanel/mappedCenters/MappedCenters';
import TeacherDashboard from './pages/screens/teacherPanel/teacherDashboard/TeacherDashboard';
import ScheduleEvents from './pages/screens/teacherPanel/scheduleEvents/ScheduleEvents';
import CenterTraineeDashboard from './pages/screens/centreTraineePanel/centerTraineeDashboard/CenterTraineeDashboard';
import CenterStudentsFaculty from './pages/screens/centreTraineePanel/centerStudentsFacultyPanel/CenterStudentsFaculty';
import Courses from './pages/screens/directorPanel/courses/CreateCourses';
import Batches from './pages/screens/directorPanel/batches/CreateBatches';
import CenterTraineeLiveViewer from './pages/screens/centreTraineePanel/centerTraineeLiveViewer/CenterTraineeLiveViewer';
import CenterTraineeOverview from './pages/screens/centreTraineePanel/overView/CennterTraineeOverview';
import StudentInfo from './pages/screens/parentPanel/studentsInfo/StudentInfo';
import CenterInfo from './pages/screens/parentPanel/centerInfo/CenterInfo';
import ParentDashboard from './pages/screens/parentPanel/parentDashboard/ParentDashboard';
import AiTutor from './pages/screens/studentPanel/aiTutor/AiTutor';
import CreateTestForStudents from './pages/screens/centreTraineePanel/createTestPanel/CreateTestForStudents';
import Events from './pages/screens/directorPanel/events/Events';
import Subjects from './pages/screens/directorPanel/subjects/Subjects';
import LearnPractically from './pages/screens/studentPanel/learnPractically/LearnPractically';
import JeePrepHome from './pages/LandPages/JeePrepHome';
import AboutUs from './pages/LandPages/AboutUs';
import UpcomingEvents from './pages/LandPages/UpcomingEvents';
import ListBatchStudents from './pages/screens/centreTraineePanel/evaluator/ListBatchStudents';
import ProblemSolver from './pages/screens/studentPanel/problemSolver/ProblemSolver';
import PaperBasedTest from './pages/screens/centreTraineePanel/paperBasedTest/PaperBasedTest';
import AiMission from './pages/screens/studentPanel/aiMission/AiMission';
import NewDashboard from './pages/screens/studentPanel/Dashboard/NewDashboard';
import StudentAttendance from './pages/screens/parentPanel/studentAttendance/StudentAttendance';
import OverallPerformance from './pages/screens/parentPanel/overallPerformance/OverallPerformance';
import NewDirectorDashboard from './pages/screens/directorPanel/dashboard/NewDirectorDashboard';
import Recommendation from './pages/screens/studentPanel/recommendation/Recommendation';
import NotificationHandler from './notifications/NotificationHandler';
import AddMentor from './pages/screens/directorPanel/addMentor/AddMentor';
import ListMentor from './pages/screens/directorPanel/listMentor/ListMentor';
// import PerformanceBasedDashboard from './pages/screens/studentPanel/Dashboard/PerformanceBasedDashboard';
import YouTubeEmbed from './pages/screens/studentPanel/problemSolver/YouTubeVidoes';

const App = () => {
  const isAuthenticated = !!sessionStorage.token;

  const elements = useRoutes([
    { path: '/', element: <LandPage /> },
    { path: 'neet', element: <NeetPrepHome /> },
    { path: 'jee', element: <JeePrepHome /> },
    { path: 'aboutus', element: <AboutUs /> },
    { path: 'upcomingevents', element: <UpcomingEvents /> },
    { path: 'auth', element: isAuthenticated ? <Navigate to={'/sasthra'} replace /> : <Login /> },
    { path: 'ai-mission', element: <Recommendation /> },
    {
      path: `sasthra`,
      element: isAuthenticated ? <Home /> : <Navigate to={'/login'} replace />,
      children: [
        { index: true, element: <Dashboard /> },
        {
          path: 'director',
          element: <ProtectedRoute allowedRoles={['director']} />,
          children: [
            { index: true, element: <DirectorDashboard /> },
            { path: 'director-inbox', element: <Inbox /> },
            { path: 'add-center', element: <AddCenter /> },
            { path: 'add-kota-teachers', element: <AddKotaTeachers /> },
            { path: 'list-center', element: <ListCenters /> },
            { path: 'list-kota', element: <ListKotaTeachers /> },
            { path: 'process-selector', element: <ProcessSelector /> },
            { path: 'course', element: <Courses /> },
            { path: 'batch', element: <Batches /> },
            { path: 'events', element: <Events /> },
            { path: 'subjects', element: <Subjects /> },
            { path: 'dashboard', element: <NewDirectorDashboard /> },
            { path: 'add-mentor', element: <AddMentor />},
            { path: 'list-mentor', element: <ListMentor />},
          ]
        },
        {
          path: 'student',
          element: <ProtectedRoute allowedRoles={['student']} />,
          children: [
            { index: true, path: 'student-dashboard', element: <StudentsDashboard /> },
            { path: 'recorded-videos', element: <RecordedVideos /> },
            { path: 'live-streaming', element: <LiveStreaming /> },
            { path: 'ebook-centre', element: <EBookCentre /> },
            { path: 'create-your-own-test', element: <CreateYourOwnTest /> },
            { path: 'mock-test-simulation', element: <MockTestSimulation /> },
            { path: 'booster-module', element: <BoosterModule /> },
            { path: 'student-community', element: <StudentCommunity /> },
            { path: 'ai-tutor', element: <AiTutor /> },
            { path: 'problem-solver', element: <ProblemSolver /> },
            { path: 'learn-practically', element: <LearnPractically /> },
            { path: 'ai-mission', element: <AiMission /> },
            { path: 'dashboard', element: <NewDashboard /> },
            { path: 'recommendation', element: <Recommendation /> }
          ]
        },
        {
          path: 'center-counselor',
          element: <ProtectedRoute allowedRoles={['center_counselor']} />,
          children: [
            {
              index: true,
              path: 'center-counselor-dashboard',
              element: <CentreCounselorDashboard />
            },
            { path: 'add-students', element: <AddStudents /> },
            { path: 'add-faculty', element: <AddFaculty /> },
            { path: 'list-students', element: <ListStudents /> },
            { path: 'list-faculty', element: <ListFaculty /> },
            { path: 'kota-teachers', element: <KotaTeachers /> }
          ]
        },
        {
          path: 'teacher',
          element: <ProtectedRoute allowedRoles={['kota_teacher']} />,
          children: [
            {
              index: true,
              path: 'teacher-dashboard',
              element: <TeacherDashboard />
            },
            { path: 'mapping-centers', element: <MappedCenters /> },
            { path: 'live-streaming', element: <TeacherLiveStreaming /> },
            { path: 'schedule-events', element: <ScheduleEvents /> }
          ]
        },
        {
          path: 'faculty',
          element: <ProtectedRoute allowedRoles={['faculty']} />,
          children: [
            {
              index: true,
              path: 'centre-trainee-dashboard',
              element: <CenterTraineeDashboard />
            },
            { path: 'students', element: <CenterStudentsFaculty /> },
            { path: 'other-faculty', element: <CenterTraineeLiveViewer /> },
            { path: 'overview', element: <CenterTraineeOverview /> },
            { path: 'live-viewer', element: <CenterTraineeLiveViewer /> },
            { path: 'paper-based-evaluator', element: <CreateTestForStudents /> },
            { path: 'create-test', element: <CreateTestForStudents /> },
            { path: 'paper-based-test', element: <PaperBasedTest /> },
            {
              path: 'evaluator-result',
              element: <ListBatchStudents />
            }
          ]
        },
        {
          path: 'parent',
          element: <ProtectedRoute allowedRoles={['parent']} />,
          children: [
            { index: true, element: <ParentDashboard /> },
            { path: 'student-info', element: <StudentInfo /> },
            { path: 'center-info', element: <CenterInfo /> },
            { path: 'student-attendance', element: <StudentAttendance /> },
            { path: 'overall-performance', element: <OverallPerformance /> }
          ]
        }
      ]
    },

    { path: '*', element: <PageNotFound /> }
  ]);
  return (
    <>
     <NotificationHandler/>
     {elements}
    </>
  );
};

export default App;
