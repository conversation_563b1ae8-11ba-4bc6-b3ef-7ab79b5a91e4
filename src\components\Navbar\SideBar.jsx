import React, { useEffect, useMemo } from 'react';
import { useLocation } from 'react-router';
import { useDispatch, useSelector } from 'react-redux';
import {
  BookCheck,
  CircleSlash2,
  CircleUser,
  ClipboardList,
  Edit,
  Calendar,
  MapPin,
  LucideLayoutDashboard,
  MapIcon,
  FileCheck,
  CheckCircle,
  FileText,
  Subtitles,
  CalendarHeart,
  List,
  School,
  Users,
  LineChart,
  Settings,
  BookOpenCheck,
  Video,
  UserPlus,
  UserCheck,
  Save,
  Info,
  ChartSpline
} from 'lucide-react';
import { MdAllInbox, MdOutlineLiveTv } from 'react-icons/md';
import { CiMemoPad } from 'react-icons/ci';
import { GoVideo } from 'react-icons/go';
import { BsLightningCharge } from 'react-icons/bs';
import { RiUserCommunityFill } from 'react-icons/ri';
import { FaChalkboardTeacher } from 'react-icons/fa';
import { BiChalkboard } from 'react-icons/bi';
import { GiTeacher } from 'react-icons/gi';

import Button from '../Field/Button';
import { setMenuData, useLazyGetRoleBasedMenuServiceQuery } from './navbar.slice';

const SideBar = ({ isOpen }) => {
  const [getMenu] = useLazyGetRoleBasedMenuServiceQuery();
  const dispatch = useDispatch();
  const menus = useSelector((state) => state.menu.menuData);
  const location = useLocation();
  const role = sessionStorage.role;

  useEffect(() => {
    if (!role) return;

    const fetchMenu = async () => {
      try {
        const res = await getMenu(role).unwrap();
        dispatch(setMenuData(res));
      } catch (err) {
        console.error('Failed to fetch menu:', err);
      }
    };

    fetchMenu();
  }, [dispatch, getMenu, role]);

  const bgClass = useMemo(() => {
    const map = {
      director: 'bg-director',
      student: 'bg-student',
      center_counselor: 'bg-counselor',
      kota_teacher: 'bg-teacher',
      faculty: 'bg-trainee',
      parent: 'bg-parents'
    };
    return map[role] || 'bg-white';
  }, [role]);

  const iconMap = useMemo(
    () => ({
      Overview: <LucideLayoutDashboard size={20} />,
      'Faculty Register': <CircleUser size={20} />,
      'Students Info': <Subtitles size={20} />,
      'E Book Center': <BookCheck />,
      'Create Your Own Center': <ClipboardList size={20} />,
      'Create Your Own Test': <CiMemoPad size={20} />,
      'Mock Test Simulation': <CiMemoPad size={20} />,
      'Recorded Video': <GoVideo size={20} />,
      'Booster Module': <BsLightningCharge size={20} />,
      'Student Community': <RiUserCommunityFill size={20} />,
      'AI - Tutor': <FaChalkboardTeacher size={20} />,
      Events: <CalendarHeart size={20} />,
      'Add Teachers': <Users size={20} />,
      Batches: <GiTeacher size={20} />,
      Courses: <GiTeacher size={20} />,
      'Add Center': <School size={20} />,
      'List Centers': <List size={20} />,
      'List Teachers': <List size={20} />,
      'Process Selector': <Settings size={20} />,
      Subjects: <BookOpenCheck size={20} />,
      Mapping: <MapIcon size={20} />,
      Dependencies: <CircleSlash2 size={20} />,
      'Live Streaming': <MdOutlineLiveTv size={20} />,
      Inbox: <MdAllInbox size={20} />,
      'Learn Practically': <BiChalkboard size={20} />,
      'Mapping Centers': <MapPin size={20} />,
      'Schedule Events': <Calendar size={20} />,
      'Live Viewer': <Video size={20} />,
      'Create Test': <FileText size={20} />,
      'Add Students': <UserPlus size={20} />,
      'Add Faculty': <UserCheck size={20} />,
      'List Students': <Users size={20} />,
      'List Faculty': <Edit size={20} />,
      'Student Info': <CircleUser size={20} />,
      'Center Info': <CircleUser size={20} />,
      Attendance: <Calendar size={20} />,
      'Overall Performance': <LineChart size={20} />,
      'Paper Based Evaluator': <FileCheck size={20} />,
      'Evaluator Result': <CheckCircle size={20} />,
      'Paper Based Test': <FileText size={20} />,
      'PBE Result': <FileText size={20} />,
      'Problem Solver': <FileText size={20} />,
      Dashboard: <ChartSpline size={20} />,
      Recommendation: <BiChalkboard size={20} />,

      Evaluator: <Save size={20} />,
      Default: <Info size={18} />
    }),
    []
  );

  const getButtonClass = (path) => {
    const isActive = location.pathname === path;
    return `
      flex items-center px-2 py-2 font-semibold transition duration-200
      ${isActive ? 'bg-indigo-100 text-indigo-600 rounded-md' : 'text-white hover:text-blue-600 hover:bg-blue-200 rounded-md'}
    `;
  };

  return (
    <div
      className={`
        ${bgClass} flex flex-col justify-start items-center transition-all duration-300 ease-in-out px-2 h-[100vh]
        ${isOpen ? ' w-60' : ' w-16'}
      `}>
      <nav className="flex flex-col gap-2 py-4">
        {menus?.length > 0 ? (
          menus.map((item) => (
            <Button
              key={item.href || item.name}
              name={isOpen ? item.name : ''}
              icon={iconMap[item.name] || iconMap.Default}
              link={item.href}
              tooltip={isOpen ? '' : item.name}
              tooltipPlace="right"
              tooltipId={item.navigate}
              className={getButtonClass(item.href)}
            />
          ))
        ) : (
          <div className="text-center text-white text-sm italic">
            {!getMenu.length && 'Failed to fetch the Menus'}
          </div>
        )}
      </nav>
    </div>
  );
};

export default SideBar;
